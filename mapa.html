<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: 'Nunito', Arial, sans-serif;
      background: #f7f9fb;
      margin: 0;
      padding: 0;
      color: #222;
    }
    h1 {
      text-align: center;
      margin-top: 2.5rem;
      font-size: 2.6rem;
      font-weight: 900;
      color: #2563eb;
      letter-spacing: 0.01em;
      margin-bottom: 2.2rem;
    }
    .main-flex {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100vw;
      max-width: none;
      margin: 2rem 0 2rem 0;
      gap: 48px;
    }
    .side-panel {
      background: #fff;
      box-shadow: 0 8px 32px 0 rgba(37,99,235,0.10), 0 2px 8px 0 rgba(0,123,255,0.08);
      border-radius: 24px;
      border: 2.5px solid #e3e8f0;
      padding: 2.8rem 2rem 2.8rem 2.4rem;
      min-width: 320px;
      max-width: 420px;
      transition: box-shadow 0.25s, background 0.25s;
      position: sticky;
      top: 32px;
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      height: 900px;
      overflow-y: auto;
    }
    .side-panel h2 {
      font-size: 1.45rem;
      font-weight: 900;
      letter-spacing: 0.01em;
      margin-bottom: 1.3rem;
      background: none;
      position: sticky;
      top: 0;
      z-index: 2;
      padding-bottom: 0.7rem;
      background: #fff;
      color: #2563eb;
    }
    .search-box {
      margin-bottom: 1.5rem;
      border-radius: 12px;
      border: 2px solid #e0e7ef;
      padding: 16px 22px;
      font-size: 1.15rem;
      background: #f7f9fb;
      transition: border 0.2s;
      position: sticky;
      top: 54px;
      z-index: 3;
      outline: none;
      color: #222;
    }
    .search-box:focus {
      border: 2px solid #2563eb;
      background: #fff;
    }

    /* Legenda pro genderové rozlišení */
    .gender-legend {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 20px;
      border: 2px solid #e0e7ef;
      position: sticky;
      top: 120px;
      z-index: 3;
    }

    .gender-legend h3 {
      font-size: 14px;
      font-weight: 700;
      color: #2563eb;
      margin: 0 0 10px 0;
      text-align: center;
    }

    .gender-items {
      display: flex;
      justify-content: space-around;
      gap: 10px;
    }

    .gender-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #666;
    }

    .gender-dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid #fff;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .gender-dot.female {
      background: #ec4899;
    }

    .gender-dot.male {
      background: #3b82f6;
    }

    /* Toggle tlačítko pro zobrazení všech/jednotlivce */
    .view-toggle-container {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 20px;
      border: 2px solid #e0e7ef;
      position: sticky;
      top: 200px;
      z-index: 3;
    }







    .view-toggle-container h3 {
      font-size: 14px;
      font-weight: 700;
      color: #2563eb;
      margin: 0 0 10px 0;
      text-align: center;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 30px;
      margin: 0 auto;
      display: block;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-radius: 30px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 22px;
      width: 22px;
      left: 4px;
      bottom: 4px;
      background: white;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-radius: 50%;
      box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    input:checked + .toggle-slider {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(30px);
    }

    .toggle-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 11px;
      color: #666;
    }

    .toggle-label.active {
      color: #2563eb;
      font-weight: 600;
    }

    /* Režim skrytí všech markerů */
    .office-map-container.single-view .marker {
      opacity: 0.1;
      pointer-events: none;
      transition: all 0.3s ease;
    }

    .office-map-container.single-view .marker.active,
    .office-map-container.single-view .marker.spotlight {
      opacity: 1;
      pointer-events: all;
    }

    /* Statistiky zobrazení */
    .view-stats {
      background: rgba(37, 99, 235, 0.1);
      border-radius: 8px;
      padding: 8px 12px;
      margin-top: 10px;
      text-align: center;
      font-size: 11px;
      color: #2563eb;
      font-weight: 600;
    }

    /* Hint zprávy */
    .toggle-hint {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.8);
      background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
      color: white;
      padding: 20px 30px;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3);
      z-index: 10000;
      opacity: 0;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .toggle-hint.show {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }

    .hint-content {
      display: flex;
      align-items: center;
      gap: 15px;
      font-weight: 600;
      font-size: 16px;
    }

    .hint-content i {
      font-size: 24px;
      animation: pointingHand 1.5s ease-in-out infinite;
    }

    @keyframes pointingHand {
      0%, 100% { transform: translateY(0) rotate(0deg); }
      50% { transform: translateY(-5px) rotate(10deg); }
    }

    /* Vylepšené not-found zprávy */
    .not-found-message {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
      color: white;
      padding: 15px 20px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
      z-index: 10000;
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 600;
    }

    .not-found-message.show {
      transform: translateX(0);
      opacity: 1;
    }

    .not-found-message i {
      font-size: 18px;
    }

    /* Info panel */
    .map-info-panel {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 20px;
      border: 2px solid #e0e7ef;
      position: sticky;
      top: 280px;
      z-index: 3;
    }

    .map-info-panel h3 {
      font-size: 14px;
      font-weight: 700;
      color: #2563eb;
      margin: 0 0 12px 0;
      text-align: center;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 11px;
      color: #666;
      padding: 4px;
    }

    .info-item i {
      font-size: 12px;
      width: 14px;
      text-align: center;
    }

    .info-item i.fa-venus {
      color: #ec4899;
    }

    .info-item i.fa-mars {
      color: #3b82f6;
    }

    .info-item i.fa-users {
      color: #10b981;
    }

    .info-item i.fa-building {
      color: #f59e0b;
    }
    .employee-list {
      gap: 1.2rem;
      padding-top: 0.5rem;
      scrollbar-width: thin;
      scrollbar-color: #b3c0d1 #f7f7fa;
      flex: 1 1 auto;
      overflow-y: auto;
      max-height: 700px;
    }
    .employee-list li {
      display: flex;
      align-items: center;
      background: #f8faff;
      border-radius: 18px;
      box-shadow: 0 2px 12px rgba(37,99,235,0.07);
      padding: 16px 22px 16px 16px;
      margin-bottom: 0;
      font-size: 1.15rem;
      font-weight: 700;
      cursor: pointer;
      border: 2px solid transparent;
      transition: box-shadow 0.18s, background 0.18s, transform 0.18s, border 0.18s;
      min-height: 60px;
      position: relative;
      color: #222;
    }
    .employee-list li:hover, .employee-list li.active {
      background: linear-gradient(90deg, #e3f0ff 0%, #f7faff 100%);
      box-shadow: 0 4px 18px rgba(37,99,235,0.13);
      border: 2px solid #2563eb33;
      transform: translateY(-2px) scale(1.03);
      color: #2563eb;
    }
    .employee-list li:focus {
      outline: 2px solid #2563eb;
    }
    .avatar-img {
      width: 52px;
      height: 52px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 20px;
      border: 2.5px solid #e3e8f0;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.09);
      vertical-align: middle;
      flex-shrink: 0;
    }
    .employee-list .emp-name {
      font-weight: 900;
      font-size: 1.15rem;
      color: #222;
      margin-right: auto;
      letter-spacing: 0.01em;
      display: block;
      line-height: 1.2;
    }
    .find-btn {
      background: #2563eb;
      color: #fff;
      border: none;
      border-radius: 24px;
      padding: 10px 24px;
      font-size: 1.13rem;
      margin-left: 28px;
      cursor: pointer;
      transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.18s;
      font-weight: 800;
      display: flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 2px 8px rgba(37,99,235,0.10);
      min-height: 44px;
      min-width: 80px;
      justify-content: center;
    }
    .find-btn .fa-search {
      font-size: 1.25em;
      margin-right: 4px;
      color: #fff;
      filter: drop-shadow(0 1px 2px #1e40af33);
    }
    .find-btn:hover, .find-btn:focus {
      background: #1742b5;
      color: #fff;
      box-shadow: 0 4px 16px rgba(37,99,235,0.18);
      transform: translateY(-2px) scale(1.04);
    }
    .mapa-wrapper {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      background: transparent;
      min-width: 0;
      margin-left: 0;
    }
    .office-map-container {
      position: relative;
      display: inline-block;
      background: #fff;
      box-shadow: 0 8px 32px rgba(37,99,235,0.10);
      border-radius: 24px;
      border: 2.5px solid #e3e8f0;
      padding: 0;
      margin: 0;
      max-width: 1920px;
      width: auto;
    }
    .office-map-container img {
      display: block;
      width: auto;
      max-width: 1920px;
      height: auto;
      -webkit-user-select: none;
      user-select: none;
      pointer-events: auto;
      background: transparent;
      border-radius: 24px;
      border: none;
    }
    .marker {
      width: 36px;
      height: 36px;
      background: transparent;
      border: none;
      border-radius: 50%;
      position: absolute;
      cursor: grab;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: none;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      user-select: none;
      -webkit-user-select: none;
      padding: 0;
    }

    .marker:active {
      cursor: grabbing;
    }

    .marker:hover {
      transform: scale(1.1);
      z-index: 10;
    }

    /* Genderové rozlišení markerů */
    .marker.female {
      box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.6);
    }

    .marker.male {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.6);
    }

    .marker.female:hover {
      box-shadow: 0 0 0 4px rgba(236, 72, 153, 0.8);
      transform: scale(1.15);
    }

    .marker.male:hover {
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.8);
      transform: scale(1.15);
    }

    .marker.female.active {
      box-shadow: 0 0 0 5px rgba(236, 72, 153, 1);
    }

    .marker.male.active {
      box-shadow: 0 0 0 5px rgba(59, 130, 246, 1);
    }

    .marker.female.found-pulse {
      animation: femaleMarkerPulse 2s ease-in-out infinite;
    }

    .marker.male.found-pulse {
      animation: maleMarkerPulse 2s ease-in-out infinite;
    }

    /* Lepší animace pro označení pozice */
    .marker.spotlight {
      animation: spotlightGlow 3s ease-in-out infinite;
      z-index: 20;
    }

    .marker.bounce-in {
      animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    .marker.breathing {
      animation: breathingGlow 4s ease-in-out infinite;
    }

    @keyframes spotlightGlow {
      0%, 100% {
        transform: scale(1.2);
        box-shadow:
          0 0 0 4px rgba(255, 255, 255, 0.9),
          0 0 0 8px rgba(255, 215, 0, 0.6),
          0 0 20px rgba(255, 215, 0, 0.4),
          0 0 40px rgba(255, 215, 0, 0.2);
      }
      25% {
        transform: scale(1.4);
        box-shadow:
          0 0 0 4px rgba(255, 255, 255, 1),
          0 0 0 12px rgba(255, 215, 0, 0.8),
          0 0 30px rgba(255, 215, 0, 0.6),
          0 0 60px rgba(255, 215, 0, 0.3);
      }
      50% {
        transform: scale(1.6);
        box-shadow:
          0 0 0 4px rgba(255, 255, 255, 1),
          0 0 0 16px rgba(255, 215, 0, 0.4),
          0 0 40px rgba(255, 215, 0, 0.8),
          0 0 80px rgba(255, 215, 0, 0.4);
      }
      75% {
        transform: scale(1.4);
        box-shadow:
          0 0 0 4px rgba(255, 255, 255, 1),
          0 0 0 12px rgba(255, 215, 0, 0.8),
          0 0 30px rgba(255, 215, 0, 0.6),
          0 0 60px rgba(255, 215, 0, 0.3);
      }
    }

    @keyframes bounceIn {
      0% {
        transform: scale(0.3) rotate(0deg);
        opacity: 0;
      }
      50% {
        transform: scale(1.05) rotate(180deg);
        opacity: 0.8;
      }
      70% {
        transform: scale(0.9) rotate(270deg);
        opacity: 1;
      }
      100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
      }
    }

    @keyframes breathingGlow {
      0%, 100% {
        transform: scale(1.1);
        filter: brightness(1.2) saturate(1.3);
      }
      50% {
        transform: scale(1.3);
        filter: brightness(1.5) saturate(1.6);
      }
    }

    .marker.active {
      z-index: 15;
      transform: scale(1.2);
    }

    .marker.found-pulse {
      z-index: 20;
      animation: markerFoundPulse 2s ease-in-out infinite;
    }

    .marker.extra-pulse {
      animation: markerExtraPulse 2s ease-in-out 3;
    }

    .marker.auto-highlighted {
      animation: autoHighlightPulse 3s ease-in-out;
      z-index: 25;
    }

    @keyframes autoHighlightPulse {
      0%, 100% {
        transform: scale(1.2);
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
      }
      25% {
        transform: scale(1.5);
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0.3);
      }
      50% {
        transform: scale(1.8);
        box-shadow: 0 0 0 20px rgba(37, 99, 235, 0.1);
      }
      75% {
        transform: scale(1.5);
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0.3);
      }
    }
    .marker.added {
      animation: marker-pop 0.5s;
    }
    @keyframes marker-pop {
      0% { transform: scale(0.5); opacity: 0.2; }
      60% { transform: scale(1.3); opacity: 1; }
      100% { transform: scale(1); opacity: 1; }
    }

    @keyframes markerFoundPulse {
      0%, 100% {
        transform: scale(1.2);
      }
      50% {
        transform: scale(1.4);
      }
    }

    @keyframes markerExtraPulse {
      0%, 100% {
        transform: scale(1.2);
      }
      50% {
        transform: scale(1.6);
      }
    }
    .marker .tooltip {
      min-width: 120px;
      text-align: center;
    }
    .marker:hover .tooltip, .marker.active .tooltip {
      opacity: 1;
      pointer-events: auto;
    }
    .tooltip {
      position: absolute;
      top: -44px;
      left: 50%;
      transform: translateX(-50%);
      background: #222;
      color: #fff;
      padding: 7px 16px;
      border-radius: 8px;
      font-size: 16px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s;
      z-index: 10;
      box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    .error-message {
      color: #c62828;
      background: #fff3f3;
      border: 1px solid #ffcdd2;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      margin: 2rem auto;
      max-width: 600px;
      font-size: 1.2rem;
      display: none;
    }
    .mapping-hint {
      text-align: center;
      margin-bottom: 1rem;
      color: #444;
      font-size: 1.1rem;
      background: #fffbe7;
      border-radius: 8px;
      padding: 10px 20px;
      max-width: 900px;
      margin-left: auto;
      margin-right: auto;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    .mapping-dialog {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      padding: 24px 32px;
      z-index: 10000;
      min-width: 320px;
      max-width: 90vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    .mapping-dialog label {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
    .mapping-dialog select {
      font-size: 1.1rem;
      padding: 6px 12px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 100%;
    }
    .mapping-dialog button {
      margin-top: 1rem;
      padding: 8px 18px;
      border-radius: 6px;
      border: none;
      background: #007bff;
      color: #fff;
      font-size: 1.1rem;
      cursor: pointer;
      transition: background 0.2s;
    }
    .mapping-dialog button:hover {
      background: #0056b3;
    }
    @media (max-width: 1200px) {
      .main-flex {
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
      }
      .side-panel {
        border-radius: 18px 18px 0 0;
        height: 300px;
        max-width: 100vw;
        min-width: 0;
        width: 100vw;
        margin-bottom: 0;
        padding: 2rem 1.2rem 2rem 1.2rem;
      }
      .mapa-wrapper {
        justify-content: center;
      }
      .office-map-container {
        border-radius: 0 0 18px 18px;
      }
      .office-map-container img {
        border-radius: 0 0 18px 18px;
      }
      .search-section {
        gap: 0.5rem;
      }
      .search-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
      .view-toggle-row {
        margin-top: 0.5rem;
        margin-bottom: 0.25rem;
      }
      .toggle-content {
        padding: 0.4rem 0.6rem;
      }
      .toggle-title {
        font-size: 0.7rem;
      }
    }
    .side-panel.collapsed {
      width: 38px !important;
      min-width: 38px !important;
      max-width: 38px !important;
      padding: 0 !important;
      overflow: hidden !important;
      transition: width 0.3s, min-width 0.3s, max-width 0.3s, padding 0.3s;
    }
    .side-panel.collapsed h2,
    .side-panel.collapsed .search-box,
    .side-panel.collapsed .employee-list,
    .side-panel.collapsed .find-btn,
    .side-panel.collapsed span {
      display: none !important;
    }
    .side-panel.collapsed #collapse-btn {
      left: 4px !important;
      right: auto !important;
      top: 8px !important;
    }
    .marker-avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: none;
      box-shadow: none;
      background: none;
      display: block;
    }
    .marker:hover, .marker.active {
      box-shadow: none;
      background: transparent;
      border: none;
    }
    .employee-bar {
      width: 100%;
      margin: 0 0 1.5rem 0;
      background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
      box-shadow:
        0 8px 24px rgba(37,99,235,0.06),
        0 4px 8px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
      border-radius: 20px;
      border: 1px solid rgba(37,99,235,0.1);
      padding: 1.25rem 1.5rem;
      position: relative;
      z-index: 10;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      height: auto;
      min-height: 120px;
    }

    .search-header {
      display: flex;
      align-items: center;
      gap: 3rem;
      padding: 1rem 1.5rem;
      background: rgba(255,255,255,0.7);
      border-radius: 16px;
      border: 1px solid rgba(37,99,235,0.08);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      height: 80px;
    }

    .search-section {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      min-width: 220px;
    }

    .employee-list-horizontal {
      display: flex;
      flex-direction: row;
      gap: 1rem;
      list-style: none;
      margin: 0;
      padding: 0.5rem 0;
      overflow-x: auto;
      overflow-y: hidden;
      scrollbar-width: thin;
      scrollbar-color: rgba(37,99,235,0.4) rgba(37,99,235,0.1);
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      flex: 1;
      min-width: 0;
      /* Gradient fade efekt na krajích */
      position: relative;
      margin-left: 2rem;
    }

    /* Webkit scrollbar styling */
    .employee-list-horizontal::-webkit-scrollbar {
      height: 6px;
    }

    .employee-list-horizontal::-webkit-scrollbar-track {
      background: rgba(37,99,235,0.08);
      border-radius: 6px;
      margin: 0 1rem;
    }

    .employee-list-horizontal::-webkit-scrollbar-thumb {
      background: linear-gradient(90deg, rgba(37,99,235,0.4) 0%, rgba(37,99,235,0.6) 100%);
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .employee-list-horizontal::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(90deg, rgba(37,99,235,0.6) 0%, rgba(37,99,235,0.8) 100%);
      transform: scaleY(1.2);
    }

    /* Gradient fade efekt na krajích pro indikaci scrollování */
    .employee-list-horizontal::before,
    .employee-list-horizontal::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 20px;
      pointer-events: none;
      z-index: 1;
      transition: opacity 0.3s ease;
    }

    .employee-list-horizontal::before {
      left: 0;
      background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, transparent 100%);
    }

    .employee-list-horizontal::after {
      right: 0;
      background: linear-gradient(270deg, rgba(255,255,255,0.9) 0%, transparent 100%);
    }
    .employee-list-horizontal li {
      background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
      border-radius: 12px;
      box-shadow:
        0 4px 16px rgba(37,99,235,0.06),
        0 2px 4px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
      border: 1px solid rgba(37,99,235,0.08);
      padding: 0.75rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.625rem;
      position: relative;
      overflow: hidden;
      min-width: 220px;
      max-width: 260px;
      flex-shrink: 0;
      height: 75px;
    }

    .employee-list-horizontal li::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #2563eb, #3b82f6, #06b6d4);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    .employee-list-horizontal li:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow:
        0 16px 40px rgba(37,99,235,0.12),
        0 8px 16px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
      border-color: rgba(37,99,235,0.2);
    }

    .employee-list-horizontal li:hover::before {
      opacity: 1;
    }

    .employee-list-horizontal li.active {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: #fff;
      transform: translateY(-2px) scale(1.01);
      box-shadow:
        0 12px 32px rgba(37,99,235,0.25),
        0 4px 12px rgba(37,99,235,0.15);
      border-color: #1d4ed8;
    }

    .employee-list-horizontal li.active::before {
      opacity: 1;
      background: linear-gradient(90deg, #60a5fa, #93c5fd, #dbeafe);
    }

    .employee-list-horizontal li.active .emp-name {
      color: #fff;
      font-weight: 900;
    }

    .employee-list-horizontal li.active .find-btn {
      background: rgba(255,255,255,0.15);
      color: #fff;
      border-color: rgba(255,255,255,0.2);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .employee-list-horizontal .avatar-img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(37,99,235,0.1);
      background: #fff;
      box-shadow:
        0 4px 12px rgba(0,0,0,0.08),
        0 2px 4px rgba(0,0,0,0.04);
      flex-shrink: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
    }

    .employee-list-horizontal li:hover .avatar-img {
      border-color: rgba(37,99,235,0.3);
      transform: scale(1.08);
      box-shadow:
        0 12px 24px rgba(0,0,0,0.12),
        0 4px 8px rgba(0,0,0,0.06);
    }

    .employee-list-horizontal li.active .avatar-img {
      border-color: rgba(255,255,255,0.4);
      transform: scale(1.05);
      box-shadow:
        0 8px 20px rgba(0,0,0,0.2),
        0 2px 6px rgba(0,0,0,0.1);
    }
    .employee-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.125rem;
      min-width: 0;
      overflow: hidden;
    }

    .employee-list-horizontal .emp-name {
      font-weight: 700;
      font-size: 0.85rem;
      color: #1f2937;
      letter-spacing: -0.01em;
      line-height: 1.2;
      margin: 0;
      transition: color 0.3s ease;
      white-space: normal;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
      max-width: 120px;
    }

    /* Status styly odstraněny - už se nepoužívají */
    .employee-list-horizontal .find-btn {
      background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 0.375rem 0.625rem;
      font-size: 0.75rem;
      font-weight: 600;
      box-shadow:
        0 3px 8px rgba(37,99,235,0.25),
        0 1px 3px rgba(37,99,235,0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 0.25rem;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      flex-shrink: 0;
      white-space: nowrap;
    }

    .employee-list-horizontal .find-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s ease;
    }

    .employee-list-horizontal .find-btn:hover::before {
      left: 100%;
    }

    .employee-list-horizontal .find-btn .fa-search {
      color: #fff;
      font-size: 0.9em;
      transition: transform 0.3s ease;
    }

    .employee-list-horizontal .find-btn:hover,
    .employee-list-horizontal .find-btn:focus {
      transform: translateY(-2px) scale(1.05);
      box-shadow:
        0 8px 20px rgba(37,99,235,0.3),
        0 4px 8px rgba(37,99,235,0.15);
    }

    .employee-list-horizontal .find-btn:hover .fa-search {
      transform: scale(1.1);
    }

    .employee-list-horizontal .find-btn:active {
      transform: translateY(0) scale(1.02);
    }

    /* Focus styly pro accessibility */
    .employee-list-horizontal li:focus {
      outline: 3px solid #2563eb;
      outline-offset: 2px;
      box-shadow: 0 6px 24px rgba(37,99,235,0.2);
    }

    .search-box-horizontal:focus-visible {
      outline: 3px solid #2563eb;
      outline-offset: 2px;
    }

    .employee-list-horizontal .find-btn:focus-visible {
      outline: 2px solid #2563eb;
      outline-offset: 2px;
    }
    .search-container {
      position: relative;
      width: 200px;
      flex-shrink: 0;
    }

    .search-container::before {
      content: '\f002';
      font-family: 'Font Awesome 5 Free';
      font-weight: 900;
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6b7280;
      font-size: 0.875rem;
      z-index: 1;
      pointer-events: none;
    }

    .search-box-horizontal {
      width: 100%;
      border-radius: 10px;
      border: 2px solid rgba(37,99,235,0.12);
      padding: 0.5rem 2.25rem 0.5rem 2rem;
      font-size: 0.85rem;
      background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,255,0.95) 100%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      outline: none;
      color: #1f2937;
      font-weight: 500;
      box-shadow:
        0 4px 12px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
      -webkit-backdrop-filter: blur(12px);
      backdrop-filter: blur(12px);
      height: 36px;
      position: relative;
    }

    .search-box-horizontal:focus {
      border-color: #2563eb;
      background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,250,255,1) 100%);
      box-shadow:
        0 8px 32px rgba(37,99,235,0.15),
        0 0 0 3px rgba(37,99,235,0.1),
        inset 0 1px 0 rgba(255,255,255,1);
      transform: translateY(-1px);
    }

    .search-box-horizontal::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }

    .search-clear-btn {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(107,114,128,0.1);
      border: none;
      color: #6b7280;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 8px;
      transition: all 0.3s ease;
      opacity: 0;
      pointer-events: none;
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .search-clear-btn.visible {
      opacity: 1;
      pointer-events: auto;
    }

    .search-results-count {
      display: none;
      font-size: 0.75rem;
      color: #6b7280;
      font-weight: 500;
      padding: 0.25rem 0.5rem;
      background: rgba(37,99,235,0.08);
      border-radius: 8px;
      margin-left: 0.5rem;
      white-space: nowrap;
      transition: all 0.3s ease;
    }

    .search-results-count.visible {
      display: flex;
      align-items: center;
    }

    /* Nový layout pro search sekci */
    .search-section {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .search-row {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    /* Toggle pod vyhledávacím polem */
    .view-toggle-row {
      width: 100%;
      margin-top: 0.75rem;
      margin-bottom: 0.5rem;
    }

    .view-toggle-container {
      display: flex;
      justify-content: flex-start;
    }

    .toggle-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(255, 255, 255, 0.95);
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 0.5rem 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .toggle-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: #374151;
      white-space: nowrap;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      margin: 0;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #cbd5e1;
      transition: all 0.3s ease;
      border-radius: 20px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background: white;
      transition: all 0.3s ease;
      border-radius: 50%;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .toggle-switch input:checked + .toggle-slider {
      background: #2563eb;
    }

    .toggle-switch input:checked + .toggle-slider:before {
      transform: translateX(20px);
    }

    .toggle-labels {
      display: flex;
      gap: 0.5rem;
      font-size: 0.7rem;
      font-weight: 600;
    }

    .toggle-label {
      color: #9ca3af;
      transition: all 0.3s ease;
      padding: 0.125rem 0.25rem;
      border-radius: 4px;
      white-space: nowrap;
    }

    .toggle-label.active {
      color: #2563eb;
      background: rgba(37, 99, 235, 0.1);
    }

    .search-clear-btn:hover {
      color: #dc2626;
      background: rgba(220,38,38,0.1);
      transform: translateY(-50%) scale(1.1);
    }

    /* Tooltip pro employee items */
    .employee-tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: #1f2937;
      color: #fff;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 0.9rem;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s ease;
      z-index: 1000;
      margin-bottom: 8px;
    }

    .employee-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 5px solid transparent;
      border-top-color: #1f2937;
    }

    .employee-list-horizontal li:hover .employee-tooltip {
      opacity: 1;
    }

    /* Responsive design pro employee bar */
    @media (max-width: 1024px) {
      .employee-bar {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem 1.25rem;
      }
      .search-header {
        gap: 1.5rem;
      }
      .search-section {
        min-width: 200px;
      }
      .search-container {
        width: 180px;
      }
      .employee-list-horizontal li {
        min-width: 200px;
        max-width: 240px;
      }
    }

    @media (max-width: 768px) {
      .employee-bar {
        padding: 0.875rem 1rem;
        margin: 0 0.5rem 1rem 0.5rem;
        border-radius: 16px;
      }
      .search-header {
        flex-direction: column;
        gap: 0.75rem;
        height: auto;
        padding: 0.75rem;
      }
      .search-section {
        gap: 0.75rem;
        width: 100%;
        min-width: auto;
      }
      .search-container {
        width: 100%;
        max-width: none;
      }
      .search-box-horizontal {
        font-size: 0.85rem;
        padding: 0.625rem 2.5rem 0.625rem 2.25rem;
        height: 38px;
      }

      .employee-list-horizontal {
        width: 100%;
        margin-left: 0;
      }
      .employee-list-horizontal li {
        min-width: 220px;
        max-width: 260px;
        height: 70px;
        padding: 0.75rem;
        gap: 0.625rem;
      }
      .employee-list-horizontal .avatar-img {
        width: 40px;
        height: 40px;
      }
      .employee-list-horizontal .emp-name {
        font-size: 0.875rem;
      }
      .employee-list-horizontal .find-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
      }
    }

    @media (max-width: 480px) {
      .employee-bar {
        padding: 0.75rem;
        margin: 0 0.25rem 0.75rem 0.25rem;
        border-radius: 14px;
      }
      .search-header {
        padding: 0.625rem;
        gap: 0.5rem;
      }
      .search-section {
        gap: 0.5rem;
        align-items: stretch;
      }
      .search-container {
        width: 100%;
      }
      .search-box-horizontal {
        padding: 0.5rem 2.25rem 0.5rem 2rem;
        font-size: 0.8rem;
        height: 36px;
      }

      .employee-list-horizontal li {
        min-width: 180px;
        max-width: 220px;
        height: 60px;
        padding: 0.625rem;
        gap: 0.5rem;
      }
      .employee-list-horizontal .avatar-img {
        width: 36px;
        height: 36px;
      }
      .employee-list-horizontal .emp-name {
        font-size: 0.8rem;
      }
      .employee-list-horizontal .find-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        gap: 0.25rem;
      }
    }

    /* Hlavní layout styly */
    .main-flex-custom {
      flex-direction: column;
      gap: 32px;
      align-items: stretch;
    }

    .mapa-wrapper-custom {
      margin-left: 0;
      justify-content: center;
    }

    /* Animace pro zvýraznění nalezených zaměstnanců */
    .found-pulse {
      animation: foundPulse 2s ease-in-out infinite;
    }

    .extra-pulse {
      animation: extraPulse 2s ease-in-out 3;
    }

    .found-highlight {
      animation: foundHighlight 1s ease-out;
    }

    .success-flash {
      animation: successFlash 1s ease-out;
    }

    @keyframes foundPulse {
      0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
      }
      50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
      }
    }

    @keyframes extraPulse {
      0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
      }
      50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 15px rgba(16, 185, 129, 0);
      }
    }

    @keyframes foundHighlight {
      0% {
        background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
        transform: scale(1);
      }
      25% {
        background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
        transform: scale(1.02);
      }
      100% {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: scale(1);
      }
    }

    @keyframes successFlash {
      0%, 100% {
        box-shadow:
          0 4px 16px rgba(37,99,235,0.06),
          0 2px 4px rgba(37,99,235,0.04);
      }
      50% {
        box-shadow:
          0 8px 32px rgba(16, 185, 129, 0.3),
          0 4px 16px rgba(16, 185, 129, 0.2),
          inset 0 0 0 2px rgba(16, 185, 129, 0.5);
      }
    }

    /* Toast notifikace */
    .found-toast {
      position: fixed;
      top: 2rem;
      right: 2rem;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      box-shadow:
        0 10px 25px rgba(16, 185, 129, 0.3),
        0 4px 10px rgba(16, 185, 129, 0.2);
      z-index: 10000;
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .found-toast.show {
      transform: translateX(0);
      opacity: 1;
    }

    .toast-content {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-weight: 600;
      font-size: 0.95rem;
    }

    .toast-icon {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.9);
      animation: toastIconBounce 0.6s ease-out;
    }

    @keyframes toastIconBounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-4px);
      }
      60% {
        transform: translateY(-2px);
      }
    }

    /* Responsive toast */
    @media (max-width: 768px) {
      .found-toast {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        transform: translateY(-100%);
      }

      .found-toast.show {
        transform: translateY(0);
      }
    }

    /* Search hint a not found messages */
    .search-hint, .not-found-message {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.8);
      padding: 1rem 1.5rem;
      border-radius: 12px;
      font-weight: 600;
      font-size: 0.95rem;
      z-index: 10001;
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 0.75rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .search-hint {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .not-found-message {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .search-hint.show, .not-found-message.show {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }

    .search-hint i, .not-found-message i {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    /* Duplicitní search-header styly odstraněny - už jsou definovány výše */


  </style>
</head>
<body>
  <div class="main-flex main-flex-custom">
    <div class="employee-bar">
      <div class="search-header">
        <div class="search-section">
          <div class="search-row">
            <div class="search-container">
              <input class="search-box-horizontal" type="text" id="searchInput" placeholder="Vyhledat zaměstnance...">
              <button type="button" class="search-clear-btn" id="searchClearBtn" title="Vymazat vyhledávání">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="search-results-count" id="searchResultsCount">
              <span id="resultsCountText">0 výsledků</span>
            </div>
          </div>

          <!-- Toggle pro režim zobrazení pod vyhledávacím polem -->
          <div class="view-toggle-row">
            <div class="view-toggle-container">
              <div class="toggle-content">
                <span class="toggle-title">Režim zobrazení:</span>
                <label class="toggle-switch">
                  <input type="checkbox" id="viewToggle" checked>
                  <span class="toggle-slider"></span>
                </label>
                <div class="toggle-labels">
                  <span class="toggle-label" id="singleLabel">Jeden</span>
                  <span class="toggle-label active" id="allLabel">Všichni</span>
                </div>
              </div>
            </div>
          </div>

        </div>
        <ul class="employee-list-horizontal" id="employeeList"></ul>
      </div>
    </div>
    <div class="mapa-wrapper mapa-wrapper-custom">
      <div class="office-map-container">
        <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">

      </div>
    </div>
  </div>
  <div id="img-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
  <script>
    // --- MARKERY V PIXELECH ---
    let markerPositions = [
      {
        "jmeno": "Smrček Petr",
        "left": 954,
        "top": 610
      },
      {
        "jmeno": "Mičáň Alex",
        "left": 891,
        "top": 579
      },
      {
        "jmeno": "Vích Ondřej",
        "left": 1029,
        "top": 614
      },
      {
        "jmeno": "Tůma Tomáš",
        "left": 1140,
        "top": 616
      },
      {
        "jmeno": "Hrdá Veronika",
        "left": 1691,
        "top": 170
      },
      {
        "jmeno": "Vlčková Soňa",
        "left": 1691,
        "top": 211
      },
      {
        "jmeno": "Hons Jindřich",
        "left": 1607,
        "top": 170
      },
      {
        "jmeno": "Beáta Barošová",
        "left": 1560,
        "top": 169
      },
      {
        "jmeno": "Tomek Jiří",
        "left": 697,
        "top": 108
      },
      {
        "jmeno": "Zezuláková Andrea",
        "left": 697,
        "top": 143
      },
      {
        "jmeno": "Brzobohatá Jana",
        "left": 697,
        "top": 180
      },
      {
        "jmeno": "Haufenhoferová Eva",
        "left": 697,
        "top": 213
      },
      {
        "jmeno": "Kalábová Lucie",
        "left": 730,
        "top": 107
      },
      {
        "jmeno": "Mašková Hana",
        "left": 730,
        "top": 141
      },
      {
        "jmeno": "Prihara Roman",
        "left": 730,
        "top": 180
      },
      {
        "jmeno": "Laco Dušan",
        "left": 359,
        "top": 99
      },
      {
        "jmeno": "Šrom Jakub",
        "left": 461,
        "top": 114
      },
      {
        "jmeno": "Sharashenidze Akaki",
        "left": 647,
        "top": 173
      },
      {
        "jmeno": "Bok Zbyněk",
        "left": 647,
        "top": 100
      },
      {
        "jmeno": "Mareš Jan",
        "left": 647,
        "top": 135
      },
      {
        "jmeno": "Kohoutová Kateřina",
        "left": 595,
        "top": 140
      },
      {
        "jmeno": "Ryšavý Miroslav",
        "left": 595,
        "top": 104
      },
      {
        "jmeno": "Špala Jaroslav",
        "left": 575,
        "top": 158
      },
      {
        "jmeno": "Rýdl Zdeněk",
        "left": 522,
        "top": 121
      },
      {
        "jmeno": "Karas Karel",
        "left": 575,
        "top": 118
      },
      {
        "jmeno": "Hodánek Jaroslav",
        "left": 804,
        "top": 116
      },
      {
        "jmeno": "Kníže Jaromír",
        "left": 855,
        "top": 118
      },
      {
        "jmeno": "Pecánek Tomáš",
        "left": 855,
        "top": 151
      },
      {
        "jmeno": "Bílek Milan",
        "left": 855,
        "top": 187
      },
      {
        "jmeno": "Dvořák Tomáš",
        "left": 855,
        "top": 221
      },
      {
        "jmeno": "Kryštof Boháč",
        "left": 898,
        "top": 119
      },
      {
        "jmeno": "Knop Ondřej",
        "left": 894,
        "top": 157
      },
      {
        "jmeno": "Gregor Boris",
        "left": 894,
        "top": 199
      },
      {
        "jmeno": "Nečesaný Jakub",
        "left": 978,
        "top": 121
      },
      {
        "jmeno": "Srb Václav",
        "left": 1015,
        "top": 124
      },
      {
        "jmeno": "Šmídek Filip",
        "left": 1015,
        "top": 163
      },
      {
        "jmeno": "Láník Libor",
        "left": 1015,
        "top": 205
      },
      {
        "jmeno": "Jedličková Markéta",
        "left": 1075,
        "top": 126
      },
      {
        "jmeno": "Horová Žaneta",
        "left": 1075,
        "top": 164
      },
      {
        "jmeno": "Nohejlová Jiřina",
        "left": 1120,
        "top": 131
      },
      {
        "jmeno": "Hůlová Helena",
        "left": 1120,
        "top": 166
      },
      {
        "jmeno": "Vacek Jaroslav",
        "left": 1120,
        "top": 201
      },
      {
        "jmeno": "Erhartová Pavla",
        "left": 1148,
        "top": 134
      },
      {
        "jmeno": "Boháč Kryštof",
        "left": 1148,
        "top": 173
      },
      {
        "jmeno": "Křivánek Libor",
        "left": 1199,
        "top": 173
      },
      {
        "jmeno": "Bednář Petr",
        "left": 1200,
        "top": 131
      },
      {
        "jmeno": "Jahoda Vojtěch",
        "left": 1268,
        "top": 144
      },
      {
        "jmeno": "Jindrová Petra",
        "left": 1320,
        "top": 146
      },
      {
        "jmeno": "Mesteková Alice",
        "left": 1380,
        "top": 148
      },
      {
        "jmeno": "Mácová Michaela",
        "left": 1380,
        "top": 183
      },
      {
        "jmeno": "Gabriel Martina",
        "left": 1455,
        "top": 151
      },
      {
        "jmeno": "Stašková Zuzana",
        "left": 1488,
        "top": 154
      },
      {
        "jmeno": "Česáková Andrea",
        "left": 1640,
        "top": 168
      },
      {
        "jmeno": "Máca Ondřej",
        "left": 1735,
        "top": 216
      },
      {
        "jmeno": "Vasjuňkina Varvara",
        "left": 1814,
        "top": 215
      },
      {
        "jmeno": "Pešková Monika",
        "left": 1841,
        "top": 264
      },
      {
        "jmeno": "Mňuková Kateřina",
        "left": 1871,
        "top": 578
      },
      {
        "jmeno": "Staňková Michaela",
        "left": 1871,
        "top": 542
      },
      {
        "jmeno": "Kopecká Zuzana",
        "left": 1820,
        "top": 580
      },
      {
        "jmeno": "Zelenková Jana",
        "left": 1820,
        "top": 544
      },
      {
        "jmeno": "Jindrová Jiřina",
        "left": 1752,
        "top": 572
      },
      {
        "jmeno": "Váchalová Zuzana",
        "left": 1652,
        "top": 578
      },
      {
        "jmeno": "Nardelli Magdalena",
        "left": 1554,
        "top": 578
      },
      {
        "jmeno": "Soukupová Michaela",
        "left": 1605,
        "top": 578
      },
      {
        "jmeno": "Raška Michal",
        "left": 1320,
        "top": 607
      },
      {
        "jmeno": "Tepličanec Pavel",
        "left": 1212,
        "top": 612
      },
      {
        "jmeno": "Fridrichová Katarína",
        "left": 1212,
        "top": 578
      },
      {
        "jmeno": "Sojka Alena",
        "left": 1271,
        "top": 608
      },
      {
        "jmeno": "Záviský Ondřej",
        "left": 664,
        "top": 609
      },
      {
        "jmeno": "Čermák Martin",
        "left": 721,
        "top": 612
      },
      {
        "jmeno": "Lebeda Dušan",
        "left": 721,
        "top": 577
      },
      {
        "jmeno": "Hesko Martin",
        "left": 791,
        "top": 573
      },
      {
        "jmeno": "Houdek Ondřej",
        "left": 791,
        "top": 606
      },
      {
        "jmeno": "Novák Petr",
        "left": 440,
        "top": 572
      },
      {
        "jmeno": "Chemišinec Igor",
        "left": 400,
        "top": 612
      },
      {
        "jmeno": "Lobotková Alena",
        "left": 305,
        "top": 557
      },
      {
        "jmeno": "Puchel Michal",
        "left": 148,
        "top": 484
      },
      {
        "jmeno": "Vichrová Martina",
        "left": 75,
        "top": 358
      },
      {
        "jmeno": "Drdák Josef",
        "left": 72,
        "top": 304
      },
      {
        "jmeno": "Kreuzman Jiří",
        "left": 98,
        "top": 212
      },
      {
        "jmeno": "Kánský Jiří",
        "left": 1867,
        "top": 303
      },
      {
        "jmeno": "Valent Lajos",
        "left": 1867,
        "top": 360
      },
      {
        "jmeno": "Procházková Kateřina",
        "left": 1627,
        "top": 374
      },
      {
        "jmeno": "Kurfiřtová Pavla",
        "left": 1627,
        "top": 426
      },
      {
        "jmeno": "Bočánek Stanislav",
        "left": 894,
        "top": 130
      },
      {
        "jmeno": "Radka Maňurová",
        "left": 1871,
        "top": 226
      },
      {
        "jmeno": "Sylvie Karolová",
        "left": 1823,
        "top": 333
      },
      {
        "jmeno": "František Černý",
        "left": 1085,
        "top": 616
      }
    ];

    // Pomocné funkce pro odstranění diakritiky a porovnání jmen
    function normalizeName(str) {
      return str
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .trim();
    }

    // Funkce pro detekci pohlaví podle jména
    function detectGender(fullName) {
      const firstName = fullName.split(' ')[0].toLowerCase();

      // Ženská jména (křestní jména)
      const femaleNames = [
        'alena', 'andrea', 'beáta', 'beata', 'eva', 'hana', 'helena', 'jana', 'jiřina', 'jirina',
        'kateřina', 'katerina', 'katarína', 'katarina', 'lucie', 'magdalena', 'markéta', 'marketa',
        'martina', 'michaela', 'monika', 'pavla', 'petra', 'soňa', 'sona', 'veronika', 'zuzana',
        'žaneta', 'zaneta', 'alice', 'varvara'
      ];

      // Ženská příjmení (končící na -ová)
      const hasFemaleLastName = fullName.toLowerCase().includes('ová');

      return femaleNames.includes(firstName) || hasFemaleLastName;
    }

    // Avatary se nyní načítají přímo z zamestnanci.json

    function getAvatarForName(jmeno) {
      // Avatar se nyní bere přímo z JSON dat
      const employee = zamestnanci.find(z => z.jmeno === jmeno);
      return employee ? employee.obrazek : 'img/no-person-photo.png';
    }

    // Collapsible panel
    let panelCollapsed = false;
    function togglePanel() {
      const panel = document.querySelector('.side-panel');
      panelCollapsed = !panelCollapsed;
      panel.classList.toggle('collapsed', panelCollapsed);
      document.getElementById('collapse-btn').innerHTML = panelCollapsed ? '⮞' : '⮜';
    }

    // Načtení zaměstnanců
    let zamestnanci = [];

    // Zobrazit loading state
    function showLoadingState() {
      const ul = document.getElementById('employeeList');
      ul.innerHTML = `
        <li style="
          justify-content: center;
          color: #6b7280;
          font-style: italic;
          padding: 2rem;
          background: #f8faff;
          border-radius: 16px;
          border: 2px solid #e3e8f0;
          min-height: 70px;
          display: flex;
          align-items: center;
          gap: 10px;
        ">
          <i class="fas fa-spinner fa-spin" style="color: #2563eb;"></i>
          Načítání zaměstnanců...
        </li>
      `;
    }

    // Inicializace dat
    function initializeData() {
      showLoadingState();

      // Načíst ze souboru zamestnanci.json
      fetch('zamestnanci.json')
        .then(res => {
          if (!res.ok) throw new Error('Network response was not ok');
          return res.json();
        })
        .then(data => {
          console.log('Data načtena ze souboru zamestnanci.json');
          zamestnanci = data;
          renderEmployeeList(zamestnanci);
          renderMarkers(zamestnanci);
          enableMappingMode();
        })
        .catch(error => {
          console.error('Nepodařilo se načíst soubor zamestnanci.json:', error);
          // Zobrazit chybovou hlášku
          const ul = document.getElementById('employeeList');
          ul.innerHTML = `
            <li style="
              justify-content: center;
              color: #dc2626;
              font-style: italic;
              padding: 2rem;
              background: #fef2f2;
              border-radius: 16px;
              border: 2px solid #fca5a5;
              min-height: 70px;
              display: flex;
              align-items: center;
              gap: 10px;
            ">
              <i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i>
              Chyba při načítání zaměstnanců
            </li>
          `;
        });
    }

    // Spustit inicializaci
    initializeData();

    function renderEmployeeList(list) {
      const ul = document.getElementById('employeeList');
      if (!ul) return;

      // Fade out animation
      ul.style.opacity = '0.5';
      ul.style.transform = 'translateY(10px)';

      setTimeout(() => {
        ul.innerHTML = '';
        // Zobrazit všechny výsledky s horizontálním scrollem
        list.forEach((z, index) => {
        const li = document.createElement('li');
        li.tabIndex = 0;
        li.dataset.jmeno = z.jmeno;
        // Avatar
        const avatar = document.createElement('img');
        avatar.src = getAvatarForName(z.jmeno);
        avatar.alt = z.jmeno;
        avatar.className = 'avatar-img';
        avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
        li.appendChild(avatar);

        // Employee info container
        const infoContainer = document.createElement('div');
        infoContainer.className = 'employee-info';

        // Jméno - rozdělené na řádky
        const nameElement = document.createElement('h3');
        const nameParts = z.jmeno.split(' ');
        if (nameParts.length >= 2) {
          // Křestní jméno na první řádek, příjmení na druhý
          nameElement.innerHTML = nameParts[0] + '<br>' + nameParts.slice(1).join(' ');
        } else {
          nameElement.textContent = z.jmeno;
        }
        nameElement.className = 'emp-name';
        nameElement.title = z.jmeno;
        infoContainer.appendChild(nameElement);

        // Status odstraněn - už se nezobrazuje

        li.appendChild(infoContainer);

        // Tlačítko najít
        const btn = document.createElement('button');
        btn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Najít';
        btn.className = 'find-btn';
        btn.title = 'Zobrazit na mapě';
        btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
        li.appendChild(btn);
        // Celá položka klikací
        li.onclick = e => {
          e.preventDefault();
          highlightByName(z.jmeno, true);
        };

        // Keyboard navigation
        li.addEventListener('keydown', e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            highlightByName(z.jmeno, true);
          } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            e.preventDefault();
            navigateEmployeeList(e.key === 'ArrowRight' ? 1 : -1, li);
          }
        });
          // Animace pro nové položky
          li.style.opacity = '0';
          li.style.transform = 'translateX(-20px)';
          ul.appendChild(li);

          // Staggered animation
          setTimeout(() => {
            li.style.transition = 'all 0.3s ease';
            li.style.opacity = '1';
            li.style.transform = 'translateX(0)';
          }, index * 50);
        });

        // Fade in animation pro celý seznam
        setTimeout(() => {
          ul.style.transition = 'all 0.3s ease';
          ul.style.opacity = '1';
          ul.style.transform = 'translateY(0)';
        }, 100);
      }, 150);
    }

    function renderMarkers(list) {
      const container = document.querySelector('.office-map-container');
      // Odstranit staré markery
      container.querySelectorAll('.marker').forEach(m => m.remove());
      // Vykreslit nové markery podle markerPositions
      markerPositions.forEach((pos, i) => {
        const idx = list.findIndex(z => z.jmeno === pos.jmeno);
        if (idx === -1) return;
        const marker = document.createElement('div');

        // Detekce pohlaví a přidání genderové třídy
        const isFemaleName = detectGender(pos.jmeno);
        marker.className = `marker ${isFemaleName ? 'female' : 'male'}`;

        marker.style.top = pos.top + 'px';
        marker.style.left = pos.left + 'px';
        marker.dataset.jmeno = pos.jmeno;
        marker.tabIndex = 0;

        // Genderově rozlišené kolečko
        const genderDot = document.createElement('div');
        genderDot.style.width = '26px';
        genderDot.style.height = '26px';
        genderDot.style.borderRadius = '50%';
        genderDot.style.background = isFemaleName ? '#ec4899' : '#3b82f6'; // Růžová pro ženy, modrá pro muže
        genderDot.style.boxShadow = isFemaleName ?
          '0 2px 8px rgba(236, 72, 153, 0.3)' :
          '0 2px 8px rgba(59, 130, 246, 0.3)';
        genderDot.style.border = '2.5px solid #fff';
        genderDot.style.display = 'block';
        genderDot.style.transition = 'all 0.3s ease';
        marker.appendChild(genderDot);
        // Tooltip (skrytý, zobrazí se při hoveru/aktivaci)
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip';
        tooltip.innerHTML = `${pos.jmeno}<br><span class='coords'>left: ${pos.left}, top: ${pos.top}</span>`;
        marker.appendChild(tooltip);
        marker.addEventListener('click', () => {
          highlightEmployee(pos.jmeno, false);
          highlightByName(pos.jmeno, true);
        });
        // Drag & drop (pouze pokud je povoleno v konfiguraci)
        if (CONFIG.enableDragAndDrop) {
          marker.addEventListener('mousedown', function(e) {
            e.preventDefault();
            startDragMarker(marker, i, e);
          });
        }
        container.appendChild(marker);
      });
    }

    // Keyboard navigation pro horizontální seznam
    function navigateEmployeeList(direction, currentElement) {
      const allItems = Array.from(document.querySelectorAll('.employee-list-horizontal li'));
      const currentIndex = allItems.indexOf(currentElement);
      let nextIndex = currentIndex + direction;

      // Wrap around
      if (nextIndex < 0) nextIndex = allItems.length - 1;
      if (nextIndex >= allItems.length) nextIndex = 0;

      if (allItems[nextIndex]) {
        allItems[nextIndex].focus();
        allItems[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    }

    // Vylepšené vyhledávání s debounce
    let searchTimeout;
    const searchInput = document.getElementById('searchInput');
    const searchClearBtn = document.getElementById('searchClearBtn');

    function updateClearButton() {
      if (searchInput.value.trim()) {
        searchClearBtn.classList.add('visible');
      } else {
        searchClearBtn.classList.remove('visible');
      }
    }

    function updateResultsCount(count, isSearching) {
      const resultsCount = document.getElementById('searchResultsCount');
      const resultsText = document.getElementById('resultsCountText');

      if (isSearching && count >= 0) {
        resultsText.textContent = count === 1 ? '1 výsledek' : `${count} výsledků`;
        resultsCount.classList.add('visible');
      } else {
        resultsCount.classList.remove('visible');
      }
    }

    searchInput.addEventListener('input', function() {
      const val = this.value.toLowerCase().trim();
      updateClearButton();

      // Debounce pro lepší výkon
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (val === '') {
          // Zobrazit všechny zaměstnance
          renderEmployeeList(zamestnanci);
          updateResultsCount(zamestnanci.length, false);
        } else {
          // Filtrovat podle jména (podporuje částečné shody)
          const filtered = zamestnanci.filter(z => {
            const name = normalizeName(z.jmeno);
            const searchTerm = normalizeName(val);
            return name.includes(searchTerm);
          });

          // Zobrazit všechny filtrované výsledky s horizontálním scrollem
          renderEmployeeList(filtered);
          updateResultsCount(filtered.length, true);

          // Pokud je pouze jeden výsledek, automaticky ho zvýrazni
          if (filtered.length === 1) {
            setTimeout(() => {
              highlightByName(filtered[0].jmeno, true);
              // Zobrazit hint pro uživatele
              showSearchHint(filtered[0].jmeno);
            }, 500);
          } else if (filtered.length === 0 && val.length > 2) {
            // Zobrazit "nenalezeno" zprávu
            showNotFoundMessage(val);
          }
        }
      }, 200);
    });

    // Clear button functionality
    searchClearBtn.addEventListener('click', function() {
      searchInput.value = '';
      updateClearButton();
      renderEmployeeList(zamestnanci); // Zobrazí všechny zaměstnance
      updateResultsCount(zamestnanci.length, false);
      searchInput.focus();
    });

    // Vylepšené zvýraznění s animacemi
    function highlightByName(jmeno, scrollTo) {
      // Nejdříve zrušit všechna předchozí zvýraznění
      clearAllHighlights();

      // Najít a zvýraznit marker na mapě
      const activeMarker = findAndHighlightMarker(jmeno, scrollTo);

      // Najít a zvýraznit položku v seznamu
      const activeEmployee = findAndHighlightEmployee(jmeno, scrollTo);

      // Pokud byl nalezen zaměstnanec, spustit animace
      if (activeEmployee || activeMarker) {
        showFoundAnimation(jmeno);
      }
    }

    // Zrušit všechna zvýraznění
    function clearAllHighlights() {
      document.querySelectorAll('.marker').forEach(marker => {
        marker.classList.remove('active', 'found-pulse');
      });
      document.querySelectorAll('.employee-list-horizontal li').forEach(li => {
        li.classList.remove('active', 'found-highlight');
      });
    }

    // Najít a zvýraznit marker na mapě
    function findAndHighlightMarker(jmeno, scrollTo) {
      let foundMarker = null;
      document.querySelectorAll('.marker').forEach(marker => {
        const isActive = marker.dataset.jmeno === jmeno;
        if (isActive) {
          foundMarker = marker;
          marker.classList.add('active', 'found-pulse');

          if (scrollTo) {
            // Smooth scroll s delay pro lepší UX
            setTimeout(() => {
              marker.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
              });

              // Přidat extra pulsing efekt po scrollu
              setTimeout(() => {
                marker.classList.add('extra-pulse');
                setTimeout(() => marker.classList.remove('extra-pulse'), 2000);
              }, 500);
            }, 300);
          }
        }
      });
      return foundMarker;
    }

    // Najít a zvýraznit zaměstnance v seznamu
    function findAndHighlightEmployee(jmeno, scrollTo) {
      let foundEmployee = null;
      document.querySelectorAll('.employee-list-horizontal li').forEach(li => {
        const isActive = li.dataset.jmeno === jmeno;
        if (isActive) {
          foundEmployee = li;
          li.classList.add('active', 'found-highlight');

          if (scrollTo) {
            // Smooth scroll k aktivní položce
            setTimeout(() => {
              li.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
              });
            }, 100);
          }
        }
      });
      return foundEmployee;
    }

    // Zobrazit animaci "nalezeno"
    function showFoundAnimation(jmeno) {
      // Vytvořit toast notifikaci
      showFoundToast(jmeno);

      // Přidat dočasný highlight efekt
      setTimeout(() => {
        const activeElements = document.querySelectorAll('.active');
        activeElements.forEach(el => {
          el.classList.add('success-flash');
          setTimeout(() => el.classList.remove('success-flash'), 1000);
        });
      }, 600);

      // Volitelný zvukový feedback (pouze pokud uživatel interagoval)
      playFoundSound();

      // Zobrazit tooltip na markeru
      showMarkerTooltip(jmeno);
    }

    // Přehrát zvuk nalezení (volitelné)
    function playFoundSound() {
      try {
        // Vytvořit krátký beep zvuk pomocí Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
      } catch (e) {
        // Zvuk není podporován nebo je zakázán
        console.log('Audio feedback není dostupný');
      }
    }

    // Zobrazit tooltip na markeru
    function showMarkerTooltip(jmeno) {
      const marker = document.querySelector(`.marker[data-jmeno="${jmeno}"]`);
      if (marker) {
        const tooltip = marker.querySelector('.tooltip');
        if (tooltip) {
          tooltip.style.opacity = '1';
          tooltip.style.pointerEvents = 'auto';

          // Skrýt tooltip po 3 sekundách
          setTimeout(() => {
            tooltip.style.opacity = '0';
            tooltip.style.pointerEvents = 'none';
          }, 3000);
        }
      }
    }

    // Toast notifikace
    function showFoundToast(jmeno) {
      // Odstranit existující toast
      const existingToast = document.querySelector('.found-toast');
      if (existingToast) existingToast.remove();

      // Vytvořit nový toast
      const toast = document.createElement('div');
      toast.className = 'found-toast';
      toast.innerHTML = `
        <div class="toast-content">
          <i class="fas fa-map-marker-alt toast-icon"></i>
          <span>Nalezen: <strong>${jmeno}</strong></span>
        </div>
      `;

      document.body.appendChild(toast);

      // Animace zobrazení
      setTimeout(() => toast.classList.add('show'), 100);

      // Automatické skrytí
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // --- KONFIGURACE ---
    const CONFIG = {
      enableMappingMode: true, // Změňte na true pro povolení mapovacího režimu
      enableDragAndDrop: true  // Změňte na true pro povolení drag & drop
    };



    // --- MAPOVACÍ REŽIM ---
    let mapped = [...markerPositions];
    function enableMappingMode() {
      if (!CONFIG.enableMappingMode) return;

      const img = document.getElementById('office-map-img');
      img.style.cursor = 'crosshair';
      img.addEventListener('click', onMapClick);
    }
    function onMapClick(e) {
      const img = e.target;
      const rect = img.getBoundingClientRect();
      const left = Math.round(e.clientX - rect.left);
      const top = Math.round(e.clientY - rect.top);
      showMappingDialog(left, top);
    }
    function showMappingDialog(left, top) {
      // Dialog s výběrem jména
      const dialog = document.createElement('div');
      dialog.className = 'mapping-dialog';
      dialog.innerHTML = `
        <label for="mapping-select">Vyberte zaměstnance:</label>
        <select id="mapping-select">
          <option value="">-- Vyberte --</option>
          ${zamestnanci.map(z => `<option value="${z.jmeno}">${z.jmeno}</option>`).join('')}
        </select>
        <div>Souřadnice: <b>left: ${left}, top: ${top}</b></div>
        <button id="mapping-confirm">Přidat</button>
        <button id="mapping-cancel" style="background:#eee;color:#333;">Zrušit</button>
      `;
      document.body.appendChild(dialog);
      dialog.querySelector('#mapping-cancel').onclick = () => dialog.remove();
      dialog.querySelector('#mapping-confirm').onclick = () => {
        const jmeno = dialog.querySelector('#mapping-select').value;
        if (!jmeno) return;
        mapped.push({ jmeno, left, top });
        console.log('Aktuální pole markerPositions:', JSON.stringify(mapped, null, 2));
        dialog.remove();
        // Přidat marker na mapu s animací
        markerPositions = mapped;
        renderMarkers(zamestnanci);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => {
            if (m.dataset.jmeno === jmeno) m.classList.add('added');
          });
        }, 50);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => m.classList.remove('added'));
        }, 800);
      };
    }

    // Kontrola načtení obrázku
    const img = document.getElementById('office-map-img');
    img.onerror = function() {
      document.querySelector('.office-map-container').style.display = 'none';
      document.getElementById('img-error').style.display = 'block';
    };
    img.onload = function() {
      document.getElementById('img-error').style.display = 'none';
      document.querySelector('.office-map-container').style.display = 'inline-block';
      renderMarkers(zamestnanci);
    };

    // Drag & drop logika
    let dragMarker = null;
    let dragIdx = null;
    let dragOffsetX = 0;
    let dragOffsetY = 0;
    function startDragMarker(marker, idx, e) {
      if (!CONFIG.enableDragAndDrop) return;

      dragMarker = marker;
      dragIdx = idx;
      const rect = marker.getBoundingClientRect();
      dragOffsetX = e.clientX - rect.left;
      dragOffsetY = e.clientY - rect.top;
      document.addEventListener('mousemove', onDragMarker);
      document.addEventListener('mouseup', stopDragMarker);
      marker.classList.add('active');
      showTooltip(marker, markerPositions[idx].left, markerPositions[idx].top, true);
    }
    function onDragMarker(e) {
      if (!dragMarker) return;
      const container = document.querySelector('.office-map-container');
      const crect = container.getBoundingClientRect();
      let left = Math.round(e.clientX - crect.left - dragOffsetX + 13); // +13 pro střed markeru
      let top = Math.round(e.clientY - crect.top - dragOffsetY + 13);
      // Omezit na hranice obrázku
      left = Math.max(0, Math.min(left, container.offsetWidth - 26));
      top = Math.max(0, Math.min(top, container.offsetHeight - 26));
      dragMarker.style.left = left + 'px';
      dragMarker.style.top = top + 'px';
      showTooltip(dragMarker, left, top, true);
    }
    function stopDragMarker(e) {
      if (!dragMarker) return;
      const left = parseInt(dragMarker.style.left);
      const top = parseInt(dragMarker.style.top);
      markerPositions[dragIdx].left = left;
      markerPositions[dragIdx].top = top;
      dragMarker.classList.remove('active');
      showTooltip(dragMarker, left, top, false);
      dragMarker = null;
      dragIdx = null;
      document.removeEventListener('mousemove', onDragMarker);
      document.removeEventListener('mouseup', stopDragMarker);
      // Vypiš nové pole do konzole
      console.log('Aktuální pole markerPositions:', JSON.stringify(markerPositions, null, 2));
    }
    function showTooltip(marker, left, top, dragging) {
      const tooltip = marker.querySelector('.tooltip');
      tooltip.innerHTML = `${marker.dataset.jmeno}<br><span class='coords'>left: ${left}, top: ${top}${dragging ? ' <b>(přesouváš)</b>' : ''}</span>`;
      tooltip.style.opacity = 1;
    }

    // Zobrazit hint při automatickém nalezení
    function showSearchHint(jmeno) {
      const hint = document.createElement('div');
      hint.className = 'search-hint';
      hint.innerHTML = `
        <i class="fas fa-lightbulb"></i>
        Automaticky nalezen: <strong>${jmeno}</strong>
      `;

      document.body.appendChild(hint);

      setTimeout(() => hint.classList.add('show'), 100);
      setTimeout(() => {
        hint.classList.remove('show');
        setTimeout(() => hint.remove(), 300);
      }, 2500);
    }

    // Zobrazit zprávu "nenalezeno"
    function showNotFoundMessage(searchTerm) {
      const message = document.createElement('div');
      message.className = 'not-found-message';
      message.innerHTML = `
        <i class="fas fa-search"></i>
        Nenalezen zaměstnanec: "<strong>${searchTerm}</strong>"
      `;

      document.body.appendChild(message);

      setTimeout(() => message.classList.add('show'), 100);
      setTimeout(() => {
        message.classList.remove('show');
        setTimeout(() => message.remove(), 300);
      }, 2000);
    }

    // Zpracování URL parametrů pro automatické zvýraznění zaměstnance
    function handleURLParameters() {
      const urlParams = new URLSearchParams(window.location.search);
      const employeeName = urlParams.get('employee');
      const shouldHighlight = urlParams.get('highlight') === 'true';

      if (employeeName && shouldHighlight) {
        console.log('=== AUTOMATICKÉ VYHLEDÁVÁNÍ ===');
        console.log('Hledám zaměstnance:', employeeName);
        console.log('Dostupní zaměstnanci na mapě:', markerPositions.map(m => m.jmeno));

        // Počkat na načtení dat a pak vyhledat
        setTimeout(() => {
          // Najít zaměstnance v datech
          let foundEmployee = null;

          // Hledat v markerPositions
          markerPositions.forEach(marker => {
            if (normalizeName(marker.jmeno) === normalizeName(employeeName)) {
              foundEmployee = marker;
              console.log('Nalezen v markerPositions:', marker.jmeno);
            }
          });

          if (foundEmployee) {
            // Nastavit vyhledávání
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
              searchInput.value = foundEmployee.jmeno;

              // Spustit vyhledávání
              const searchEvent = new Event('input', { bubbles: true });
              searchInput.dispatchEvent(searchEvent);
            }

            // Zobrazit hint o automatickém nalezení
            setTimeout(() => {
              showSearchHint(foundEmployee.jmeno);

              // Automaticky zvýraznit marker s lepší animací
              if (highlightEmployee(foundEmployee.jmeno, true)) {
                console.log('Zvýrazněn marker:', foundEmployee.jmeno);

                // Pokud je v režimu jednotlivce, přepnout na zobrazení všech
                if (!isShowingAll) {
                  const toggle = document.getElementById('viewToggle');
                  toggle.checked = true;
                  toggle.dispatchEvent(new Event('change'));
                }
              }
            }, 800);
          } else {
            console.log('Zaměstnanec nenalezen na mapě:', employeeName);
            showNotFoundMessage(employeeName);
          }
        }, 1500); // Počkat na načtení dat
      }
    }

    // Toggle funkcionalita pro zobrazení všech/jednotlivce
    let isShowingAll = true;
    let currentActiveMarker = null;

    // Načíst nastavení z localStorage
    function loadViewSettings() {
      const savedSetting = localStorage.getItem('mapViewMode');
      if (savedSetting !== null) {
        isShowingAll = savedSetting === 'all';
      }
      return isShowingAll;
    }

    // Uložit nastavení do localStorage
    function saveViewSettings(showAll) {
      localStorage.setItem('mapViewMode', showAll ? 'all' : 'single');
    }

    function initializeViewToggle() {
      const toggle = document.getElementById('viewToggle');
      const singleLabel = document.getElementById('singleLabel');
      const allLabel = document.getElementById('allLabel');
      const mapContainer = document.querySelector('.office-map-container');

      // Načíst uložené nastavení
      isShowingAll = loadViewSettings();
      toggle.checked = isShowingAll;

      toggle.addEventListener('change', function() {
        isShowingAll = this.checked;

        // Uložit nastavení do localStorage
        saveViewSettings(isShowingAll);

        if (isShowingAll) {
          // Zobrazit všechny
          mapContainer.classList.remove('single-view');
          allLabel.classList.add('active');
          singleLabel.classList.remove('active');

          // Odstranit všechny animace
          document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('spotlight', 'breathing', 'bounce-in');
          });

          currentActiveMarker = null;
        } else {
          // Režim jednotlivce
          mapContainer.classList.add('single-view');
          singleLabel.classList.add('active');
          allLabel.classList.remove('active');

          // Pokud není žádný aktivní, zobrazit hint
          if (!currentActiveMarker) {
            showToggleHint();
          }
        }


      });

      // Aplikovat počáteční stav
      if (isShowingAll) {
        mapContainer.classList.remove('single-view');
        allLabel.classList.add('active');
        singleLabel.classList.remove('active');
      } else {
        mapContainer.classList.add('single-view');
        singleLabel.classList.add('active');
        allLabel.classList.remove('active');
      }
    }

    function showToggleHint() {
      const hint = document.createElement('div');
      hint.className = 'toggle-hint';
      hint.innerHTML = `
        <div class="hint-content">
          <i class="fas fa-hand-pointer"></i>
          <p>Klikněte na zaměstnance pro zobrazení</p>
        </div>
      `;

      document.body.appendChild(hint);

      setTimeout(() => hint.classList.add('show'), 100);
      setTimeout(() => {
        hint.classList.remove('show');
        setTimeout(() => hint.remove(), 300);
      }, 3000);
    }



    // Vylepšená funkce pro zvýraznění zaměstnance
    function highlightEmployee(employeeName, useSpotlight = false) {
      // Odstranit předchozí animace
      document.querySelectorAll('.marker').forEach(marker => {
        marker.classList.remove('spotlight', 'breathing', 'bounce-in', 'active');
      });

      // Najít správný marker
      const targetMarker = Array.from(document.querySelectorAll('.marker')).find(marker => {
        return normalizeName(marker.dataset.jmeno) === normalizeName(employeeName);
      });

      if (targetMarker) {
        currentActiveMarker = targetMarker;

        if (!isShowingAll) {
          targetMarker.classList.add('active');
        }

        if (useSpotlight) {
          // Spotlight animace pro automatické vyhledávání
          targetMarker.classList.add('spotlight');
          setTimeout(() => {
            targetMarker.classList.remove('spotlight');
            targetMarker.classList.add('breathing');
          }, 3000);
        } else {
          // Bounce-in animace pro manuální klik
          targetMarker.classList.add('bounce-in');
          setTimeout(() => {
            targetMarker.classList.remove('bounce-in');
            targetMarker.classList.add('breathing');
          }, 800);
        }

        // Scroll k markeru
        targetMarker.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        });


        return true;
      }

      return false;
    }



    // Fallback pro chybějící obrázky
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.avatar-img').forEach(img => {
        img.onerror = () => { img.src = 'img/no-person-photo.png'; };
      });

      // Inicializovat toggle
      initializeViewToggle();

      // Zpracovat URL parametry
      handleURLParameters();


    });
  </script>
</body>
</html>